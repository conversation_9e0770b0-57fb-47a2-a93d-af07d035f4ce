.hero-slider-wrapper {
    width: 100%;
    position: relative;
}

.hero-slider {
    width: 100%;
    transition: height 0.3s ease;
}

.hero-slide-image,
.hero-slide-video {
    width: 100%;
    overflow: hidden;
}

.hero-slide-image img {
    margin: 0 auto;
    max-height: calc(100vh - var(--header-height)) !important;
    width: 100%;
    object-fit: contain;
}

.hero-slide-video {
    position: relative;
    width: 100%;
    height: auto;
}

.hero-slide-video video {
    width: 100%;
    height: 100%;
    max-height: calc(100vh - var(--header-height));
    object-fit: cover;
    filter: opacity(1); {# Fix black border around video on windows #}
}

.hero-slider-wrapper .splide__arrow {
    background-color: white;
}
