(function() {
    'use strict';

    function loadSplide(callback) {
        // Check if Splide is already loaded
        if (window.Splide) {
            callback();
            return;
        }

        const cssLink = document.createElement('link');
        cssLink.rel = 'stylesheet';
        cssLink.href = 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css';
        document.head.appendChild(cssLink);

        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js';
        script.onload = callback;
        script.onerror = function() {
            console.error('Failed to load Splide.js from CDN');
        };
        document.head.appendChild(script);
    }

    function initializeHeroSliders() {
        const heroSliders = document.querySelectorAll('.hero-slider');

        heroSliders.forEach(function(slider) {
            // Skip if already initialized
            if (slider.classList.contains('splide-initialized')) {
                return;
            }

            // Get the number of slides
            const slides = slider.querySelectorAll('.splide__slide');
            const slideCount = slides.length;

            // Only initialize if there are slides
            if (slideCount === 0) {
                return;
            }

            const splideOptions = {
                type: 'loop',
                autoplay: slideCount > 1,
                interval: 6000,
                pauseOnHover: true,
                pauseOnFocus: true,
                resetProgress: false,
                arrows: slideCount > 1,
                pagination: slideCount > 1,
                keyboard: true,
                drag: slideCount > 1,
                wheel: false,
                releaseWheel: false,
                waitForTransition: true,
                updateOnMove: true,
                trimSpace: false,
                focus: 'center',
                breakpoints: {
                    768: {
                        arrows: false,
                        autoplay: false,
                    }
                }
            };

            try {
                const splide = new Splide(slider, splideOptions);
                splide.mount();
                slider.classList.add('splide-initialized');
            } catch (error) {
                console.error('Error initializing hero slider:', error);
            }
        });
    }

    function init() {
        loadSplide(initializeHeroSliders);
    }

    // Initialize based on document ready state
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();
